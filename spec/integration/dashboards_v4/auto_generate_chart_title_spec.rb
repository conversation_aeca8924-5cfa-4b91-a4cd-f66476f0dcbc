# frozen_string_literal: true
# typed: false

require 'rails_helper'

DATASET_TREE_SELECTOR = '.ci-data-set-select'

describe 'Auto-generate chart title feature', :js do
  include_context 'dashboards_v4'

  let(:admin) { get_test_admin }
  let(:query_model_sql) do
    <<~SQL
      SELECT *
        FROM (VALUES
          ('2024-02-03T10:00:00.000000'::timestamp, 45,  'Electronics', 3, 1250.75, 'North', 'Premium', 'Online', 'Laptops'),
          ('2024-02-20T15:30:00.000000'::timestamp, 30,  'Electronics', 5, 950.25, 'South', 'Regular', 'Retail', 'Phones'),
          ('2024-03-10T09:15:00.000000'::timestamp, 55,  'Clothing', 8, 125.50, 'East', 'Premium', 'Online', 'Outerwear'),
          ('2024-04-05T14:45:00.000000'::timestamp, -10, 'Groceries', 12, 45.99, 'West', 'Regular', 'Retail', 'Produce'),
          ('2024-05-15T11:30:00.000000'::timestamp, 0,  'Home Goods', 4, 320.00, 'South', 'Business', 'Retail', 'Kitchen'),
          ('2024-06-25T16:20:00.000000'::timestamp, 80,  'Electronics', 9, 1800.00, 'East', 'Premium', 'Online', 'Audio'),
          ('2024-07-15T08:30:45.000000'::timestamp, 35, 'Electronics', 4, 899.99, 'North', 'Premium', 'Online', 'Tablets'),
          ('2024-08-20T14:45:30.000000'::timestamp, 28, 'Electronics', 6, 499.50, 'South', 'Regular', 'Retail', 'Accessories'),
          ('2024-09-10T09:15:00.000000'::timestamp, 42, 'Clothing', 3, 139.99, 'East', 'Business', 'Online', 'Casual'),
          ('2024-10-05T11:30:15.000000'::timestamp, -5, 'Clothing', 7, 249.50, 'West', 'Regular', 'Retail', 'Formal'),
          ('2024-11-22T16:00:45.000000'::timestamp, 60, 'Groceries', 9, 65.75, 'North', 'Premium', 'Online', 'Organic'),
          ('2024-12-14T08:45:00.000000'::timestamp, -12, 'Groceries', 11, 37.25, 'South', 'Regular', 'Retail', 'Frozen'),
          ('2025-01-10T13:20:30.000000'::timestamp, 75, 'Books', 5, 95.50, 'East', 'Business', 'Online', 'Educational'),
          ('2025-01-18T10:00:15.000000'::timestamp, 22, 'Books', 8, 75.25, 'West', 'Regular', 'Retail', 'Children'),
          ('2025-01-25T15:10:45.000000'::timestamp, 50, 'Home Goods', 2, 450.00, 'North', 'Premium', 'Online', 'Furniture'),
          ('2025-02-01T12:45:00.000000'::timestamp, 15, 'Home Goods', 6, 125.75, 'South', 'Regular', 'Retail', 'Appliances'),
          ('2024-07-05T17:45:00.000000'::timestamp, -8,  'Groceries', 15, 78.25, 'West', 'Business', 'Retail', 'Dairy'),
          ('2024-08-15T13:10:00.000000'::timestamp, 95,  'Books', 6, 150.00, 'North', 'Premium', 'Retail', 'Non-Fiction'),
          ('2024-09-20T09:30:00.000000'::timestamp, 40,  'Home Goods', 2, 89.99, 'South', 'Regular', 'Online', 'Decor'),
          ('2025-02-03T17:30:20.000000'::timestamp, 65, 'Books', 3, 125.75, 'East', 'Premium', 'Online', 'Biography')
        )
      AS t (date_and_time, value, category, quantity, price, region, customer_type, sales_channel, subcategory)
    SQL
  end

  def base_dashboard_aml
    <<~STR
      Dashboard visual_table {
        title: 'Visual Table'
        description: ''''''
        view: CanvasLayout {
          label: 'View 1'
          height: 840
          grid_size: 20
          mobile {
            mode: 'auto'
          }
        }
      }
    STR
  end

  def select_viz_field(model_name, field_name)
    wait_for_element_load('.ci-empty-field')
    select_h_select_option('.viz-section:nth-child(1) .ci-empty-field', value: "#{model_name}$!#{field_name}")
  end

  def recompile_aml
    # Ctrl + E to recompile the AML
    page.driver.browser.action
        .key_down(:control)
        .send_keys('e')
        .key_up(:control)
        .perform
  end

  # Common setup for all visual regression tests
  before do
    dashboard_table_no_timezone.update!(definition_aml: base_dashboard_aml)
    FeatureToggle.toggle_global(Dashboard::FT_V4_CREATION, true)
    qlogin(admin, "/dashboards/v4/#{dashboard_table_no_timezone.id}/edit")

    wait_for_element_load { page.find_by_id('block-v1') }
    recompile_aml
    expect(page).to have_no_css('#block-v1')
  end

  describe 'chart title auto-generation' do
    context 'when creating new visualizations' do
      it 'auto-generates titles for data table visualizations' do
        # Create a new viz block
        wait_and_click('[data-ci="add-viz-block"]')
        wait_for_element_load(DATASET_TREE_SELECTOR)
        select_h_select_option(DATASET_TREE_SELECTOR, label: 'test_data_set')
        select_h_select_option('.viz-section:nth-child(1) .ci-empty-field', label: 'Date And Time')
        select_h_select_option('.viz-section:nth-child(1) .ci-empty-field', label: 'Value')
        select_h_select_option('.viz-section:nth-child(1) .ci-empty-field', label: 'Category')
        select_h_select_option('.viz-section:nth-child(1) .ci-empty-field', label: 'Quantity')
        select_h_select_option('.viz-section:nth-child(1) .ci-empty-field', label: 'Price')
        select_h_select_option('.viz-section:nth-child(1) .ci-empty-field', label: 'Region')
        safe_click('.ci-explorer-control-get-results')
        wait_for_loading_finish

        # Check that title is auto-generated (should not be empty)
        title_input = page.find('.h-data-set-report-editor .title span')
        expect(title_input.text).to eq('Date And Time, Value, Category, Quantity, Price, and Region')
      end

      # continue finishing test for other viz type

      it 'auto-generates titles for line chart visualizations' do
        wait_and_click('[data-ci="add-viz-block"]')
        wait_for_element_load('.ci-data-set-select')

        select_h_select_option('.ci-data-set-select', label: "#{dataset.id}ds")
        select_viz_field('users', 'id')

        # Switch to line chart
        safe_click('[data-ci="viz-type-selector"]')
        safe_click('[data-ci="viz-type-line_chart"]')
        wait_for_loading_finish

        safe_click('.ci-save-ds-based-report')
        wait_for_element_load('[data-hui-section="resolve-button"]')

        title_input = page.find('.h-input')
        expect(title_input.value).not_to be_empty
        expect(title_input.value).to match(/id/i)

        safe_click('[data-hui-section="resolve-button"]')
        wait_for_element_load('[data-ci="block-controls"]')
      end

      it 'auto-generates titles for pie chart visualizations' do
        wait_and_click('[data-ci="add-viz-block"]')
        wait_for_element_load('.ci-data-set-select')

        select_h_select_option('.ci-data-set-select', value: "#{dataset.id}ds")
        select_viz_field('users', 'id')

        # Switch to pie chart
        safe_click('[data-ci="viz-type-selector"]')
        safe_click('[data-ci="viz-type-pie_chart"]')
        wait_for_loading_finish

        safe_click('.ci-save-ds-based-report')
        wait_for_element_load('[data-hui-section="resolve-button"]')

        title_input = page.find('.h-input')
        expect(title_input.value).not_to be_empty
        expect(title_input.value).to match(/id/i)

        safe_click('[data-hui-section="resolve-button"]')
        wait_for_element_load('[data-ci="block-controls"]')
      end
    end

    context 'when editing existing visualizations' do
      let(:existing_viz_block) do
        canvas_dashboard.definition['blocks'].find { |block| block['type'] == 'viz' }
      end

      it 'updates auto-generated title when fields change' do
        # Navigate to existing viz block
        page.find("#block-#{existing_viz_block['id']}").hover
        safe_click('[data-icon="edit"]')
        wait_for_element_load('.viz-section')

        # Add another field
        select_viz_field('users', 'full_name')
        wait_for_loading_finish

        # Check that title updates to include new field
        # Note: This tests the eventBus.$emit('vizInput:title', chartTitle) flow
        expect(page).to have_css('.dataset-title input')
        title_input = page.find('.dataset-title input')

        # Wait for title to update
        wait_expect(5) do
          title_input.value.include?('full_name')
        end

        expect(title_input.value).to include('id')
        expect(title_input.value).to include('full_name')
      end
    end

    context 'edge cases' do
      it 'handles empty data gracefully' do
        # Create viz with filter that returns no data
        wait_and_click('[data-ci="add-viz-block"]')
        wait_for_element_load('.ci-data-set-select')

        select_h_select_option('.ci-data-set-select', value: "#{dataset.id}ds")
        select_viz_field('users', 'id')

        # Add a filter that will return no results
        safe_click('[data-ci="add-filter"]')
        wait_for_element_load('.filter-condition')

        # Set filter to impossible value
        page.find('.filter-value input').set('impossible_value_12345')
        wait_for_loading_finish

        safe_click('.ci-save-ds-based-report')
        wait_for_element_load('[data-hui-section="resolve-button"]')

        # Should still generate a title even with no data
        title_input = page.find('.h-input')
        expect(title_input.value).not_to be_empty

        safe_click('[data-hui-section="resolve-button"]')
      end

      it 'handles special characters in field names' do
        # This would test fields with special characters if they exist in the dataset
        # For now, just verify basic functionality works
        wait_and_click('[data-ci="add-viz-block"]')
        wait_for_element_load('.ci-data-set-select')

        select_h_select_option('.ci-data-set-select', value: "#{dataset.id}ds")
        select_viz_field('users', 'id')
        wait_for_loading_finish

        safe_click('.ci-save-ds-based-report')
        wait_for_element_load('[data-hui-section="resolve-button"]')

        title_input = page.find('.h-input')
        expect(title_input.value).not_to be_empty
        expect(title_input.value).to be_a(String)

        safe_click('[data-hui-section="resolve-button"]')
      end
    end

    context 'negative cases' do
      it 'does not override manually set titles' do
        wait_and_click('[data-ci="add-viz-block"]')
        wait_for_element_load('.ci-data-set-select')

        select_h_select_option('.ci-data-set-select', value: "#{dataset.id}ds")
        select_viz_field('users', 'id')
        wait_for_loading_finish

        safe_click('.ci-save-ds-based-report')
        wait_for_element_load('[data-hui-section="resolve-button"]')

        # Manually set a custom title
        title_input = page.find('.h-input')
        custom_title = 'My Custom Title'
        title_input.set(custom_title)

        # Add another field - title should not change since it was manually set
        # Note: This tests the logic in NewDatasetPreview.vue that checks
        # if current title equals lastGeneratedTitle before updating
        page.find('.viz-section').click # Click somewhere to trigger field addition UI
        select_viz_field('users', 'full_name')
        wait_for_loading_finish

        # Title should remain the custom title
        expect(title_input.value).to eq(custom_title)

        safe_click('[data-hui-section="resolve-button"]')
      end
    end
  end
end
