diff --git a/app/javascript/modules/Viz/components/VizResult.vue b/app/javascript/modules/Viz/components/VizResult.vue
index 4455a7e2e8b..7f313ed1390 100644
--- a/app/javascript/modules/Viz/components/VizResult.vue
+++ b/app/javascript/modules/Viz/components/VizResult.vue
@@ -940,6 +940,7 @@ async function updateFn (renderOptions: Record<string, any>, vizSetting: VizSett
   const {
     options, message, pivotRenderer: pvRenderer, chartTitle, query, drillthroughs: dthroughts, executedAql,
   } = vizInput as any;
+  console.debug('chartTitle', chartTitle);
 
   pivotRenderer.value = pvRenderer;
 
diff --git a/app/javascript/modules/Viz/services/input/ConversionFunnelInput.js b/app/javascript/modules/Viz/services/input/ConversionFunnelInput.js
index 278f351c93c..5fbe313233a 100644
--- a/app/javascript/modules/Viz/services/input/ConversionFunnelInput.js
+++ b/app/javascript/modules/Viz/services/input/ConversionFunnelInput.js
@@ -8,7 +8,7 @@ export default class ConversionFunnelInput extends VizInput {
     const { vizSetting } = this;
     const {
       data: { formats },
-      data: pivoted, query, meta, executedAql,
+      data: pivoted, chartTitle, query, meta, executedAql,
     } = await this.pivot(generatorOptions);
 
     const columns = ConversionFunnelInput.buildColumns(pivoted, formats);
@@ -33,6 +33,7 @@ export default class ConversionFunnelInput extends VizInput {
       },
       message: null,
       pivotRenderer: pivoted,
+      chartTitle,
       query,
       executedAql,
     };
diff --git a/app/javascript/modules/Viz/services/input/CustomChartInput.ts b/app/javascript/modules/Viz/services/input/CustomChartInput.ts
index 94263042dc6..cf755f424c6 100644
--- a/app/javascript/modules/Viz/services/input/CustomChartInput.ts
+++ b/app/javascript/modules/Viz/services/input/CustomChartInput.ts
@@ -67,7 +67,7 @@ export default class CustomChartInput extends VizInput {
     }
 
     const {
-      data, fields, query, executedAql,
+      data, fields, query, executedAql, chartTitle,
     } = await this.sortPaginate({
       ...generatorOptions,
       sort: sortOpts,
@@ -117,6 +117,7 @@ export default class CustomChartInput extends VizInput {
           containerOverflow: !autoSize, // set overflow in case there's no autosize
         },
       },
+      chartTitle,
       query,
       executedAql,
     };
diff --git a/app/javascript/modules/Viz/services/input/DataTableInput.js b/app/javascript/modules/Viz/services/input/DataTableInput.js
index 85bf1efd675..84a806eaab7 100644
--- a/app/javascript/modules/Viz/services/input/DataTableInput.js
+++ b/app/javascript/modules/Viz/services/input/DataTableInput.js
@@ -80,6 +80,7 @@ export default class DataTableInput extends VizInput {
       message: null,
       query: options.meta.query,
       executedAql: options.meta.executedAql,
+      chartTitle: options.chartTitle,
     };
   }
 
diff --git a/app/javascript/modules/Viz/services/input/DataTableInputGeneratedOptions.js b/app/javascript/modules/Viz/services/input/DataTableInputGeneratedOptions.js
index 23c4f10fbd4..1f654237a6f 100644
--- a/app/javascript/modules/Viz/services/input/DataTableInputGeneratedOptions.js
+++ b/app/javascript/modules/Viz/services/input/DataTableInputGeneratedOptions.js
@@ -62,7 +62,7 @@ export default class DataTableInputGeneratedOptions {
     const useFixedBottomRows = checkFeatureToggle('data_table:bottom_total_average_rows');
 
     const {
-      data: result, fields, query, meta, executedAql,
+      data: result, fields, query, meta, executedAql, chartTitle,
     } = results;
 
     this.actions = results.actions || [];
@@ -76,6 +76,7 @@ export default class DataTableInputGeneratedOptions {
       this.avgRow = result.aggregated[1];
       this.avgRow[0] = useFixedBottomRows ? this.avgRow[0] : `Average (${numRows}) rows`;
     }
+    this.chartTitle = chartTitle;
 
     this.meta = {
       page: meta.page,
diff --git a/app/javascript/modules/Viz/services/input/GeoHeatmapInput.js b/app/javascript/modules/Viz/services/input/GeoHeatmapInput.js
index eaae70e65b2..fc34695a3cd 100644
--- a/app/javascript/modules/Viz/services/input/GeoHeatmapInput.js
+++ b/app/javascript/modules/Viz/services/input/GeoHeatmapInput.js
@@ -8,7 +8,7 @@ export default class GeoHeatmapInput extends VizInput {
   async generate (generatorOptions = {}) {
     const { vizSetting } = this;
     const {
-      data, query, meta, executedAql,
+      data, query, meta, executedAql, chartTitle,
     } = await this.sortPaginate(generatorOptions);
     const { fields, values: points } = data;
 
@@ -57,6 +57,7 @@ export default class GeoHeatmapInput extends VizInput {
         },
       },
       message: null,
+      chartTitle,
       query,
       executedAql,
     };
diff --git a/app/javascript/modules/Viz/services/input/MetricSheetInput.ts b/app/javascript/modules/Viz/services/input/MetricSheetInput.ts
index 03a32d15ca8..e705007fd69 100644
--- a/app/javascript/modules/Viz/services/input/MetricSheetInput.ts
+++ b/app/javascript/modules/Viz/services/input/MetricSheetInput.ts
@@ -129,7 +129,7 @@ export default class MetricSheetInput extends VizInput {
     const dateFieldFormat = date.format as OldFormatDefinition;
     const result = await this.sortPaginate(generatorOptions);
 
-    const { data, query, executedAql } = result;
+    const { data, query, executedAql, chartTitle } = result;
     const {
       fields, values, column_types: columnTypes, meta,
     } = data;
@@ -162,6 +162,7 @@ export default class MetricSheetInput extends VizInput {
         values: metricValues,
       },
       message: null,
+      chartTitle,
       query,
       executedAql,
     };
diff --git a/app/javascript/modules/Viz/services/input/NewConversionFunnelInput.js b/app/javascript/modules/Viz/services/input/NewConversionFunnelInput.js
index 0dd67d6b255..6825790a411 100644
--- a/app/javascript/modules/Viz/services/input/NewConversionFunnelInput.js
+++ b/app/javascript/modules/Viz/services/input/NewConversionFunnelInput.js
@@ -30,7 +30,7 @@ export default class NewConversionFunnelInput extends VizInput {
     const { vizSetting } = this;
     const {
       data: { formats },
-      data: pivoted, query, meta, executedAql,
+      data: pivoted, chartTitle, query, meta, executedAql,
     } = await super.pivot(generatorOptions);
     const columns = ConversionFunnelInput.buildColumns(pivoted, formats);
     const valueRows = ConversionFunnelInput.buildValues(pivoted);
@@ -113,6 +113,7 @@ export default class NewConversionFunnelInput extends VizInput {
       options,
       message: null,
       pivotRenderer: pivoted,
+      chartTitle,
       query,
       executedAql,
     };
diff --git a/app/javascript/modules/Viz/services/input/PivotTableInput.js b/app/javascript/modules/Viz/services/input/PivotTableInput.js
index 64d18bb0dbb..7b82bc0c283 100644
--- a/app/javascript/modules/Viz/services/input/PivotTableInput.js
+++ b/app/javascript/modules/Viz/services/input/PivotTableInput.js
@@ -63,6 +63,7 @@ export default class PivotTableInput extends VizInput {
       options,
       pivotRenderer,
       message: null,
+      chartTitle: options.chartTitle,
       query: options.meta.query,
       executedAql: options.meta.executedAql,
     };
diff --git a/app/javascript/modules/Viz/services/input/PivotTableInputGeneratedOptions.js b/app/javascript/modules/Viz/services/input/PivotTableInputGeneratedOptions.js
index cff4ae6bd70..3a78f9b7e7c 100644
--- a/app/javascript/modules/Viz/services/input/PivotTableInputGeneratedOptions.js
+++ b/app/javascript/modules/Viz/services/input/PivotTableInputGeneratedOptions.js
@@ -42,12 +42,14 @@ export default class PivotTableInputGeneratedOptions {
     const pageSize = this.isInfiniteScroll ? this.meta.pageSize : pageSz;
 
     const {
-      data, query, meta, executedAql,
+      data, query, meta, executedAql, chartTitle,
     } = (await this._generator.pivot({
       ...generatorOptions, page, pageSize, sort, sortByColumnType: true,
     }));
     this.postUpdatePivotTable(data);
 
+    this.chartTitle = chartTitle;
+
     this.meta = {
       page: meta.page,
       pageSize: meta.page_size,
diff --git a/app/javascript/modules/Viz/services/input/RetentionHeatmapInput.js b/app/javascript/modules/Viz/services/input/RetentionHeatmapInput.js
index 0446c3279b7..ad471bf836a 100644
--- a/app/javascript/modules/Viz/services/input/RetentionHeatmapInput.js
+++ b/app/javascript/modules/Viz/services/input/RetentionHeatmapInput.js
@@ -20,7 +20,7 @@ export default class RetentionHeatmapInput extends VizInput {
     const { vizSetting } = this;
     const {
       data: { formats },
-      data: pivoted, exploreOpts, query, meta, executedAql,
+      data: pivoted, exploreOpts, chartTitle, query, meta, executedAql,
     } = await this.pivot({
       ...generatorOptions,
       sortByColumnType: true,
@@ -76,6 +76,7 @@ export default class RetentionHeatmapInput extends VizInput {
       },
       message: null,
       pivotRenderer: pivoted,
+      chartTitle,
       query,
       executedAql,
     };
